import json

from langchain_core.tools import tool
from langgraph.types import interrupt


@tool(return_direct=True, parse_docstring=True)
def add_liquidity_action(project: str, symbol: str, amount: float) -> str:
    """
    添加流动性。包括质押，流动性挖矿， stake, liquidity provision

    Args:
        project (str): [REQUIRED] 项目或者协议名称
        symbol (str): [REQUIRED] 代币符号
        amount (float): [REQUIRED] 添加的代币数量

    Returns:
        str: json格式的链上操作结果

    """
    params = {"action_type": "add_liquidity","contract_address":"0x2655df9b30f6b80f5700163d26e55fdbf0983d87", "project":project, "symbol":symbol, "amount": amount}
    return interrupt(json.dumps(params))

def swap_action(in_token: str = None, in_amount: float = None, out_token: str = None, out_amount: float = None) -> str:
    """
    买入卖出或者兑入兑出代币。

    Args:
        in_token (str): [OPTIONAL] 输入代币符号
        in_amount (float): [OPTIONAL] 输入代币数量
        out_token (str): [OPTIONAL] 输出代币符号
        out_amount (float): [OPTIONAL] 输出代币数量

    Note:
        必须至少指定in_token+in_amount或out_token+out_amount其中一组参数

    Returns:
        str: json格式的链上操作结果

    """
    params = {"action_type": "swap","contract_address":"0x2655df9b30f6b80f5700163d26e55fdbf0983d87", "in_token":in_token, "in_amount":in_amount, "out_token": out_token, "out_amount":out_amount}
    return interrupt(json.dumps({k: v for k, v in params.items() if v is not None}))