"""Utility functions used in our graph."""
from langchain_core.messages import HumanMessage, AIMessage, ToolMessage
import logging

logger = logging.getLogger(__name__)


def split_model_and_provider(fully_specified_name: str) -> dict:
    """Initialize the configured chat model."""
    if "/" in fully_specified_name:
        provider, model = fully_specified_name.split("/", maxsplit=1)
    else:
        provider = None
        model = fully_specified_name
    return {"model": model, "provider": provider}


def get_last_user_message(messages: list) -> str:
    """Get the last user message from the messages list.

    Args:
        messages (list): List of messages from graph state.

    Returns:
        str: Content of the last user message, or empty string if no user message found.
    """
    last_user_message = None
    for message in reversed(messages):
        if isinstance(message, HumanMessage):
            last_user_message = message.content
            break

    return last_user_message if last_user_message is not None else ""


def filter_message(messages: list) -> list:
    """Filter AIMessages with tool calls that were not executed.

    Args:
        messages (list): List of messages from graph state.

    Returns:
        list: Filtered messages list.
    """
    tool_id_set = set()
    to_remove = set()

    # Single reverse pass to collect tool IDs and identify messages to remove
    for i in reversed(range(len(messages))):
        message = messages[i]
        if isinstance(message, ToolMessage):
            # Collect tool call IDs from ToolMessages
            tool_id_set.add(message.tool_call_id)
        elif isinstance(message, AIMessage) and message.tool_calls:
            # Check if all tool calls in this AIMessage have been executed
            all_executed = True
            for tool_call in message.tool_calls:
                if tool_call["id"] not in tool_id_set:
                    all_executed = False
                    break
            if not all_executed:
                # Mark this AIMessage for removal
                to_remove.add(i)
                # 消息列表中最多一条未被执行的工具指令，若发现，即可退出循环,可能有多个，全删除
                # break

    # Create filtered messages list by excluding marked indices
    filtered_messages = [msg for idx, msg in enumerate(messages) if idx not in to_remove]

    return filtered_messages


def log_token_usage(msg, function_name: str):
    """记录 LLM 调用的 token 使用统计信息

    Args:
        msg: LLM 响应消息对象
        function_name: 调用函数的名称，用于日志标识
    """
    # 调试断点 - 在这里会暂停执行
    # 方法1: 使用 debugpy (需要 VSCode 连接)
    try:
        import debugpy
        if not debugpy.is_client_connected():
            debugpy.listen(("localhost", 5678))
            print(f"🔧 调试服务器已启动，等待连接... (函数: {function_name})")
        # 如果你想在这里强制等待调试器连接，取消下面的注释
        # debugpy.wait_for_client()
        # print(f"✅ 调试器已连接到函数: {function_name}")
    except ImportError:
        print("⚠️ debugpy 未安装，跳过调试设置")

    # 方法2: 使用 pdb (命令行调试，更简单)
    # import pdb; pdb.set_trace()

    # 方法3: 仅打印调试信息
    print(f"🔍 调试信息 - 函数: {function_name}, 消息类型: {type(msg)}")

    try:
        # 尝试从 usage_metadata 获取 token 信息
        if hasattr(msg, 'usage_metadata') and msg.usage_metadata:
            usage = msg.usage_metadata
            logger.info(f"LLM Token 使用统计 ({function_name}): "
                       f"输入 tokens: {getattr(usage, 'input_tokens', 'N/A')}, "
                       f"输出 tokens: {getattr(usage, 'output_tokens', 'N/A')}, "
                       f"总计 tokens: {getattr(usage, 'total_tokens', 'N/A')}")
            return

        # 尝试从 response_metadata 获取 token 信息
        if hasattr(msg, 'response_metadata') and msg.response_metadata:
            metadata = msg.response_metadata

            # OpenAI 格式
            if 'token_usage' in metadata:
                token_usage = metadata['token_usage']
                logger.info(f"LLM Token 使用统计 ({function_name}): "
                           f"输入 tokens: {token_usage.get('prompt_tokens', 'N/A')}, "
                           f"输出 tokens: {token_usage.get('completion_tokens', 'N/A')}, "
                           f"总计 tokens: {token_usage.get('total_tokens', 'N/A')}")
                return

            # Anthropic/Google 格式
            if 'usage' in metadata:
                usage = metadata['usage']
                logger.info(f"LLM Token 使用统计 ({function_name}): "
                           f"输入 tokens: {usage.get('input_tokens', usage.get('prompt_tokens', 'N/A'))}, "
                           f"输出 tokens: {usage.get('output_tokens', usage.get('completion_tokens', 'N/A'))}, "
                           f"总计 tokens: {usage.get('total_tokens', 'N/A')}")
                return

            # 其他可能的格式
            if 'model_usage' in metadata:
                model_usage = metadata['model_usage']
                logger.info(f"LLM Token 使用统计 ({function_name}): "
                           f"输入 tokens: {model_usage.get('input_tokens', 'N/A')}, "
                           f"输出 tokens: {model_usage.get('output_tokens', 'N/A')}, "
                           f"总计 tokens: {model_usage.get('total_tokens', 'N/A')}")
                return

            logger.info(f"LLM Token 使用统计 ({function_name}): 无法获取 token 统计信息 - metadata 格式不匹配")
        else:
            logger.info(f"LLM Token 使用统计 ({function_name}): 响应中未包含 token 使用信息")

    except Exception as e:
        logger.warning(f"记录 token 使用统计时出错 ({function_name}): {e}")
