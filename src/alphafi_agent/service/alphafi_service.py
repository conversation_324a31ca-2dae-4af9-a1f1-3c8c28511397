import os
import httpx
import logging
from typing import Dict, Any, Optional


class AlphaFiService:
    def __init__(self):
        self.base_url = os.environ.get('ALPHAFI_SERVICE_BASE_URL', 'http://10.1.33.3:9050')
        self.client = None
        self.logger = logging.getLogger(__name__)
        self.initialized = False

    def initialize(self):
        """初始化AlphaFi服务"""
        # 设置超时参数为30秒
        self.client = httpx.Client(timeout=30.0)
        self.initialized = True
        self.logger.info('AlphaFi service initialized with new client')
        return self

    def recommend(
        self,
            project: str = None, symbol: str = None, symbol_fuzzy: bool = False, apy_low: float = None,
            apy_high: float = None,
            tvlUsd_low: float = None, tvlUsd_high: float = None, user_id: str = None
    ):
        """
        调用AlphaFi工作流API

        参数:
            user_input: 用户输入内容
            user: 用户标识，默认为'AlphaFi'
            response_mode: 响应模式，默认为'blocking'
            bear_token: Bearer令牌'

        返回:
            Dict[str, Any]: API响应结果
        """
        # 确保初始化
        if not self.initialized or self.client is None:
            self.initialize()

        # 构建请求头
        headers = {
            'Content-Type': 'application/json'
        }

        # 构建请求URL
        url = f'{self.base_url}/alphafi/agent/recommend/pool'


        max_retries = 2
        retry_count = 0

        while retry_count <= max_retries:
            try:
                # 构建查询参数，只包含非None的参数
                params = {
                    'project': project,
                    'symbol': symbol,
                    'symbol_fuzzy': symbol_fuzzy,
                    'apy_low': apy_low,
                    'apy_high': apy_high,
                    'tvlUsd_low': tvlUsd_low,
                    'tvlUsd_high': tvlUsd_high,
                    'user_id': user_id
                }
                # 过滤掉值为None的参数
                filtered_params = {k: v for k, v in params.items() if v is not None}
                
                # 发送get请求
                self.logger.info(f'Sending request to AlphaFi service API: {url} with params: {filtered_params}')
                response = self.client.get(
                    url=url,
                    headers=headers,
                    params=filtered_params
                )
                print(response.json())

                # 检查响应状态
                response.raise_for_status()
                return response.json()

            except httpx.TimeoutException as e:
                self.logger.error(f'Timeout error calling AlphaFi  API: {e}')
                retry_count += 1
                if retry_count <= max_retries:
                    self.logger.info(f'Retrying ({retry_count}/{max_retries})...')
                    self.initialize()  # 重新初始化客户端
                else:
                    print(f'Error calling AlphaFi  API after {max_retries} retries: {e}')
                    raise
            except httpx.HTTPError as e:
                self.logger.error(f'HTTP error calling AlphaFi  API: {e}')
                # 如果是连接错误，尝试重新初始化客户端
                if isinstance(e, httpx.ConnectError):
                    retry_count += 1
                    if retry_count <= max_retries:
                        self.logger.info(f'Reconnecting and retrying ({retry_count}/{max_retries})...')
                        self.initialize()
                    else:
                        print(f'Error calling AlphaFi  API after {max_retries} retries: {e}')
                        raise
                else:
                    print(f'Error calling AlphaFi  API: {e}')
                    raise
            except Exception as e:
                print(f'Error calling AlphaFi  API: {e}')
                raise

    def close(self):
        """关闭HTTP客户端连接"""
        if self.client:
            self.client.close()
            self.logger.info('AlphaFi service client closed')
            self.initialized = False
        else:
            self.logger.info('AlphaFi service client not initialized')


def initialize_service() -> AlphaFiService:
    """初始化AlphaFi服务"""
    service = AlphaFiService()
    service.initialize()
    return service