import redis
import json
from datetime import datetime
import os

from langgraph.constants import CONFIG_KEY_THREAD_ID

from alphafi_agent.constants import USER_ID, MESSAGES, TS


class ChatHistory:
    def __init__(self):
        self.redis = redis.Redis(host=os.getenv('REDIS_HOST'), port=int(os.getenv('REDIS_PORT')), db=int(os.getenv('REDIS_CHAT_HISTORY_DB')))
    def store_message(self, user_id, thread_id, message):
        """
        存储消息到Redis，若相同user_id和thread_id已存在则不写入
        :param user_id: 用户ID
        :param thread_id: 会话ID
        :param message: 消息内容
        :return: bool 是否成功写入
        """
        # 检查是否已存在
        if self.redis.hexists(f"user:{user_id}", thread_id):
            return False

        # 构建完整消息对象
        msg_obj = {
            USER_ID: user_id,
            CONFIG_KEY_THREAD_ID: thread_id,
            MESSAGES: message,
            TS: datetime.now().isoformat()
        }

        # 存储到Redis
        if os.getenv('REDIS_TTL'):
            ttl = int(os.getenv('REDIS_TTL'))
            self.redis.expire(f'user:{user_id}', ttl*60)  # 设置TTL转换为seconds
        self.redis.hset(f'user:{user_id}', thread_id, json.dumps(msg_obj))
        return True

    def get_user_messages(self, user_id):
        """
        获取指定用户的所有消息
        :param user_id: 用户ID
        :return: 消息对象列表
        """
        messages = self.redis.hgetall(f"user:{user_id}")
        return [json.loads(msg) for msg in messages.values()]
    
    def delete_thread(self, user_id, thread_id):
        """
        删除指定用户的指定会话
        :param user_id: 用户ID
        :param thread_id: 会话ID
        :return: bool 是否成功删除
        """
        if self.redis.hexists(f"user:{user_id}", thread_id):
            self.redis.hdel(f"user:{user_id}", thread_id)
            return True
        return False
