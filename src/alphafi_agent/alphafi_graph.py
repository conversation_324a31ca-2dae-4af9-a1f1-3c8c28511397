"""Graphs that extract memories on a schedule and handle different user intents."""
import asyncio
import logging

from langchain_core.runnables import RunnableConfig
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.graph import END, StateGraph, START
from langgraph.prebuilt import ToolNode

import alphafi_agent.prompts
from alphafi_agent import configuration, utils
from alphafi_agent.llms import get_llm
from alphafi_agent.mcp_config import MCP_SERVER_CONFIG, MCP_SERVER_CONFIG_RAG
from alphafi_agent.protocol.on_chain import add_liquidity_action, swap_action
from alphafi_agent.protocol.recommendation import recommend_pool_action
from alphafi_agent.state import State
from langchain_core.messages import ToolMessage, HumanMessage

logger = logging.getLogger(__name__)

from dotenv import load_dotenv
load_dotenv()
llm = get_llm()


# 意图处理节点
INTENT_ON_CHAIN_OPERATION = "on_chain_operation"
INTENT_RECOMMENDATION_ACTION = "recommendation_action"
INTENT_TOKEN_MARKET_DATA = "token_market_data"
INTENT_YIELD_POOL_DATA = "yield_pool_data"
INTENT_DEFI_KNOWLEDGE_QUERY = "defi_knowledge_query"

# 工具节点名称变量
TOOL_ON_CHAIN_OPERATION = "tool_on_chain_operation"
TOOL_RECOMMENDATION_ACTION = "tool_recommendation_action"
TOOL_TOKEN_MARKET_DATA = "tool_token_market_data"
TOOL_YIELD_POOL_DATA = "tool_yield_pool_data"
TOOL_DEFI_KNOWLEDGE_QUERY = "tool_defi_knowledge_query"
NODE_RECOGNIZE_INTENT = "recognize_intent"
GRAPH_NAME = "AlphaFiAgent"

# 
OUT_PUT= "output"

async def get_on_chain_operation_tools():
    # 获取基础工具列表
    llm_tools = [add_liquidity_action, swap_action]
    return llm_tools

async def get_recommend_tools():
    # 获取基础工具列表
    llm_tools = [recommend_pool_action]
    return llm_tools
async def get_token_market_tools():
    mcp_client = MultiServerMCPClient(MCP_SERVER_CONFIG)
    # 获取基础工具列表
    return [*(await mcp_client.get_tools())]

async def get_yield_pool_tools():
    mcp_client = MultiServerMCPClient(MCP_SERVER_CONFIG)
    # 获取基础工具列表
    return [*(await mcp_client.get_tools())]


async def get_defi_knowledge_tools():
    mcp_client = MultiServerMCPClient(MCP_SERVER_CONFIG_RAG)
    return [*(await mcp_client.get_tools())]


def has_image_artifact(artifact):
    """
    检查artifact是否包含图片，支持对象和字典两种格式

    Args:
        artifact: 要检查的artifact对象或字典

    Returns:
        bool: 如果包含图片返回True，否则返回False
    """
    if hasattr(artifact, 'type') and artifact.type == "image":
        return True
    elif isinstance(artifact, dict) and artifact.get("type") == "image":
        return True
    return False


async def process_tool_message_with_artifacts(tool_msg):
    """
    处理包含图片等artifact的工具消息，将其转换为模型可见的格式

    Args:
        tool_msg: 包含artifact的工具消息

    Returns:
        处理后的消息，包含图片内容
    """
    try:
        # 检查消息是否有artifact字段
        if not hasattr(tool_msg, 'artifact') or not tool_msg.artifact:
            return tool_msg

        # 构建多模态内容列表
        content_list = []

        # 添加文本内容
        if hasattr(tool_msg, 'content') and tool_msg.content:
            content_list.append({
                "type": "text",
                "text": tool_msg.content
            })

        # 提取artifact中的图片
        for artifact in tool_msg.artifact:
            if has_image_artifact(artifact):
                # 获取图片数据，支持对象和字典两种格式
                if hasattr(artifact, 'mimeType'):
                    # 对象格式
                    mime_type = getattr(artifact, 'mimeType', 'image/png')
                    data = getattr(artifact, 'data', '')
                else:
                    # 字典格式
                    mime_type = artifact.get('mimeType', 'image/png')
                    data = artifact.get('data', '')

                image_content = {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:{mime_type};base64,{data}"
                    }
                }
                content_list.append(image_content)

        # 创建新的User消息，包含多模态内容
        processed_msg = HumanMessage(
            content=content_list if len(content_list) > 1 else (tool_msg.content or ""),
            #tool_call_id=getattr(tool_msg, 'tool_call_id', ''),
            #name=getattr(tool_msg, 'name', '')
        )

        return processed_msg

    except Exception as e:
        logger.error(f"处理包含artifact的工具消息时出错: {e}")
        # 如果处理失败，返回原始消息
        return tool_msg

# Define intent recognition node
async def recognize_intent(state: State):
    filter_msgs = utils.filter_message(state.messages)
    # 强制禁止流式输出
    msg = await asyncio.get_event_loop().run_in_executor(
        None, lambda: llm.invoke(
            [{"role": "system", "content": alphafi_agent.prompts.INTENT_CLASSIFICATION_PROMPT}, *filter_msgs])
    )
    # msg = await llm.ainvoke(
    #     [{"role": "system", "content": alphafi_agent.prompts.INTENT_CLASSIFICATION_PROMPT}, *filter_msgs]
    # )
    intent = INTENT_DEFI_KNOWLEDGE_QUERY
    if INTENT_ON_CHAIN_OPERATION in msg.content:
        intent = INTENT_ON_CHAIN_OPERATION
    elif INTENT_RECOMMENDATION_ACTION in msg.content:
        intent = INTENT_RECOMMENDATION_ACTION
    elif INTENT_TOKEN_MARKET_DATA in msg.content:
        intent = INTENT_TOKEN_MARKET_DATA
    elif INTENT_YIELD_POOL_DATA in msg.content:
        intent = INTENT_YIELD_POOL_DATA
    elif "AlphaFi" in msg.content:
        # 自我介绍，直接返回
        return {"messages": [msg]}

    return {"intent": intent}

# Define intent execution nodes
async def handle_on_chain_operation(state: State):
    filter_msgs = utils.filter_message(state.messages)
    msg = await llm.bind_tools(await get_on_chain_operation_tools()).ainvoke(
        [{"role": "system", "content": alphafi_agent.prompts.ON_CHAIN_OPERATION_PROMPT}, *filter_msgs]
    )
    return {"messages": [msg]}

async def handle_recommendation_action(state: State):
    filter_msgs = utils.filter_message(state.messages)
    msg = await llm.bind_tools(await get_recommend_tools(), tool_choice="any").ainvoke(
        [{"role": "system", "content": alphafi_agent.prompts.RECOMMENDATION_PROMPT}, *filter_msgs]
    )
    return {"messages": [msg]}

async def handle_token_market_data(state: State):
    filter_msgs = utils.filter_message(state.messages)
    msg = await llm.bind_tools(await get_token_market_tools()).ainvoke(
        [{"role": "system", "content": alphafi_agent.prompts.TOKEN_MARKET_DATA_PROMPT}, *filter_msgs]
    )
    return {"messages": [msg]}

async def handle_yield_pool_data(state: State):
    filter_msgs = utils.filter_message(state.messages)
    msg = await llm.bind_tools(await get_yield_pool_tools()).ainvoke(
        [{"role": "system", "content": alphafi_agent.prompts.YIELD_POOL_DATA_PROMPT}, *filter_msgs]
    )
    return {"messages": [msg]}

async def handle_defi_knowledge_query(state: State):
    filter_msgs = utils.filter_message(state.messages)
    msg = await llm.bind_tools(await get_defi_knowledge_tools()).ainvoke(
        [{"role": "system", "content": alphafi_agent.prompts.DEFI_KNOWLEDGE_QUERY_PROMPT}, *filter_msgs]
    )
    return {"messages": [msg]}

# 工具处理节点方法
async def tool_handle_on_chain_operation(state: State, config: RunnableConfig):
    bind_tools = await get_on_chain_operation_tools()
    return {"messages": await ToolNode(bind_tools).ainvoke(state.messages, config)}

async def tool_handle_recommendation_action(state: State, config: RunnableConfig):
    bind_tools = await get_recommend_tools()
    return {"messages": await ToolNode(bind_tools).ainvoke(state.messages, config)}

async def tool_handle_token_market_data(state: State, config: RunnableConfig):
    bind_tools = await get_token_market_tools()
    return {"messages": await ToolNode(bind_tools).ainvoke(state.messages, config)}

async def tool_handle_yield_pool_data(state: State, config: RunnableConfig):
    bind_tools = await get_yield_pool_tools()
    return {"messages": await ToolNode(bind_tools).ainvoke(state.messages, config)}

async def tool_handle_defi_knowledge_query(state: State, config: RunnableConfig):
    bind_tools = await get_defi_knowledge_tools()

    # 使用ToolNode执行工具调用
    tool_messages = await ToolNode(bind_tools).ainvoke(state.messages, config)

    # 处理包含图片的工具响应
    processed_messages = []
    for msg in tool_messages:
        processed_msg = await process_tool_message_with_artifacts(msg)
        processed_messages.append(processed_msg)

    return {"messages": processed_messages}

async def handle_output(state: State):
    filter_msgs = utils.filter_message(state.messages)
    msg = await llm.ainvoke(
        [{"role": "system", "content": alphafi_agent.prompts.OUTPUT_PROMPT}, *filter_msgs]
    )
    return {"messages": [msg]}

def intent_router(state: State):
    if state.intent:
        if INTENT_ON_CHAIN_OPERATION in state.intent:
            return INTENT_ON_CHAIN_OPERATION
        elif INTENT_RECOMMENDATION_ACTION in state.intent:
            return INTENT_RECOMMENDATION_ACTION
        elif INTENT_TOKEN_MARKET_DATA in state.intent:
            return INTENT_TOKEN_MARKET_DATA
        elif INTENT_YIELD_POOL_DATA in state.intent:
            return INTENT_YIELD_POOL_DATA
        else:
            return INTENT_DEFI_KNOWLEDGE_QUERY
    else:
        msg = state.messages[-1]
        if "AlphaFi" in msg.content:
            return END
    return INTENT_DEFI_KNOWLEDGE_QUERY

def handler_router(state: State):
    """Determine the next step based on the presence of tool calls."""
    msg = state.messages[-1]
    # Check if the message has tool_calls attribute and if it's not empty
    if hasattr(msg, 'tool_calls') and msg.tool_calls:
        if INTENT_ON_CHAIN_OPERATION in state.intent:
            return TOOL_ON_CHAIN_OPERATION
        elif INTENT_RECOMMENDATION_ACTION in state.intent:
            return TOOL_RECOMMENDATION_ACTION
        elif INTENT_TOKEN_MARKET_DATA in state.intent:
            return TOOL_TOKEN_MARKET_DATA
        elif INTENT_YIELD_POOL_DATA in state.intent:
            return TOOL_YIELD_POOL_DATA
        else:
            return TOOL_DEFI_KNOWLEDGE_QUERY
    # Otherwise, finish; user can send the next message
    return END

def tool_router(state: State):
    """Determine the next step based on the presence of tool calls."""
    msg = state.messages[-1]
    if hasattr(msg, 'name') and msg.name and msg.name.endswith("_action"):
        return END
    return OUT_PUT

# Create the graph + all nodes
builder = StateGraph(State, config_schema=configuration.Configuration)

# Add nodes
builder.add_node(NODE_RECOGNIZE_INTENT, recognize_intent)
builder.add_node(INTENT_ON_CHAIN_OPERATION, handle_on_chain_operation)
builder.add_node(INTENT_RECOMMENDATION_ACTION, handle_recommendation_action)
builder.add_node(INTENT_TOKEN_MARKET_DATA, handle_token_market_data)
builder.add_node(INTENT_YIELD_POOL_DATA, handle_yield_pool_data)
builder.add_node(INTENT_DEFI_KNOWLEDGE_QUERY, handle_defi_knowledge_query)

# 新增工具节点
builder.add_node(TOOL_ON_CHAIN_OPERATION, tool_handle_on_chain_operation)
builder.add_node(TOOL_RECOMMENDATION_ACTION, tool_handle_recommendation_action)
builder.add_node(TOOL_TOKEN_MARKET_DATA, tool_handle_token_market_data)
builder.add_node(TOOL_YIELD_POOL_DATA, tool_handle_yield_pool_data)
builder.add_node(TOOL_DEFI_KNOWLEDGE_QUERY, tool_handle_defi_knowledge_query)

builder.add_node(OUT_PUT,handle_output)

# Define edges
builder.add_edge(START, NODE_RECOGNIZE_INTENT)

builder.add_conditional_edges(
    NODE_RECOGNIZE_INTENT,
    intent_router,
    [INTENT_ON_CHAIN_OPERATION, INTENT_RECOMMENDATION_ACTION, INTENT_TOKEN_MARKET_DATA, INTENT_YIELD_POOL_DATA, INTENT_DEFI_KNOWLEDGE_QUERY, END]
)

# Add edges from intent nodes to tool nodes or end node
builder.add_conditional_edges(INTENT_ON_CHAIN_OPERATION, handler_router,[TOOL_ON_CHAIN_OPERATION, END])
builder.add_conditional_edges(INTENT_RECOMMENDATION_ACTION, handler_router,[TOOL_RECOMMENDATION_ACTION, END])
builder.add_conditional_edges(INTENT_TOKEN_MARKET_DATA, handler_router,[TOOL_TOKEN_MARKET_DATA, END])
builder.add_conditional_edges(INTENT_YIELD_POOL_DATA, handler_router,[TOOL_YIELD_POOL_DATA, END])
builder.add_conditional_edges(INTENT_DEFI_KNOWLEDGE_QUERY, handler_router,[TOOL_DEFI_KNOWLEDGE_QUERY, END])

# Add edges from tool nodes to OUT_PUT
builder.add_conditional_edges(TOOL_ON_CHAIN_OPERATION, tool_router,[OUT_PUT, END])
builder.add_conditional_edges(TOOL_RECOMMENDATION_ACTION, tool_router,[OUT_PUT, END])
builder.add_conditional_edges(TOOL_TOKEN_MARKET_DATA, tool_router,[OUT_PUT, END])
builder.add_conditional_edges(TOOL_YIELD_POOL_DATA, tool_router,[OUT_PUT, END])
builder.add_conditional_edges(TOOL_DEFI_KNOWLEDGE_QUERY, tool_router,[OUT_PUT, END])

builder.add_edge(OUT_PUT, END)

alphafi_graph = builder.compile()
alphafi_graph.name = GRAPH_NAME


__all__ = ["alphafi_graph", "builder"]
