#!/usr/bin/env python3
"""
测试 token 使用统计功能的脚本
"""

import asyncio
import os
import sys
from dotenv import load_dotenv

# 添加项目路径到 Python 路径
sys.path.insert(0, 'src')

from alphafi_agent.alphafi_graph import alphafi_graph
from alphafi_agent.llms import get_llm
from alphafi_agent import utils

async def test_token_usage():
    """测试 token 使用统计功能"""
    
    # 加载环境变量
    load_dotenv()
    
    print("=== Token 使用统计测试 ===")
    print(f"当前模型提供商: {os.getenv('MODEL_PROVIDER')}")
    print(f"当前模型: {os.getenv('MODEL')}")
    print()
    
    # 测试简单的 LLM 调用
    print("1. 测试直接 LLM 调用...")
    llm = get_llm()
    
    try:
        # 简单的测试消息
        test_message = "你好，请简单介绍一下 DeFi"
        
        print(f"发送消息: {test_message}")
        
        # 调用 LLM
        response = await llm.ainvoke([{"role": "user", "content": test_message}])
        
        print(f"响应内容: {response.content[:100]}...")
        
        # 测试 token 统计函数
        utils.log_token_usage(response, "test_direct_call")
        
    except Exception as e:
        print(f"直接调用测试失败: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # 测试通过 alphafi_graph 调用
    print("2. 测试通过 alphafi_graph 调用...")
    
    try:
        # 构造测试输入
        test_input = {
            "messages": [{"role": "user", "content": "什么是流动性挖矿？"}]
        }
        
        print(f"发送消息: {test_input['messages'][0]['content']}")
        
        # 调用 alphafi_graph
        result = await alphafi_graph.ainvoke(test_input)
        
        print(f"响应消息数量: {len(result.get('messages', []))}")
        if result.get('messages'):
            last_msg = result['messages'][-1]
            print(f"最后一条消息内容: {last_msg.content[:100]}...")
        
    except Exception as e:
        print(f"alphafi_graph 调用测试失败: {e}")
    
    print("\n测试完成！请查看日志中的 token 使用统计信息。")

if __name__ == "__main__":
    asyncio.run(test_token_usage())
