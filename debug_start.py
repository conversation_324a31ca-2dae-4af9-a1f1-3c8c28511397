#!/usr/bin/env python3
"""
调试启动脚本 - 支持 VSCode 远程调试
"""

import debugpy
import os
import sys
import subprocess
from pathlib import Path

def main():
    # 启用调试服务器
    debugpy.listen(("localhost", 5678))
    print("🔧 调试服务器已启动在端口 5678")
    print("📍 在 VSCode 中按 F5 或运行 'Python: 附加到 AlphaFi Agent' 调试配置来连接")
    print("⏳ 等待调试器连接...")

    # 等待调试器连接（可选）
    # debugpy.wait_for_client()
    # print("✅ 调试器已连接！")

    # 设置环境变量
    env = os.environ.copy()
    env['PYTHONPATH'] = str(Path(__file__).parent / 'src')
    env['PYDEVD_DISABLE_FILE_VALIDATION'] = '1'  # 禁用文件验证警告

    print("🚀 尝试启动 langgraph dev...")

    try:
        # 方法1: 使用 uv run 启动 langgraph dev
        subprocess.run([
            'uv', 'run', 'langgraph', 'dev'
        ], env=env, cwd=Path(__file__).parent, check=True)

    except (subprocess.CalledProcessError, FileNotFoundError) as e:
        print(f"❌ uv run langgraph dev 启动失败: {e}")

        # 方法2: 直接启动 FastAPI 应用
        print("🔄 尝试直接启动 FastAPI 应用...")
        try:
            # 添加项目路径
            sys.path.insert(0, str(Path(__file__).parent / 'src'))

            # 导入并初始化应用
            print("📦 导入应用模块...")
            from alphafi_agent.app import __global_init__, app
            import uvicorn
            import asyncio

            # 初始化应用
            print("🔧 初始化应用...")
            asyncio.run(__global_init__())

            print("🌐 启动 FastAPI 应用在 http://localhost:8000")
            uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")

        except Exception as e2:
            print(f"❌ FastAPI 启动也失败: {e2}")
            print("💡 请检查依赖是否正确安装，或尝试手动运行: uv run langgraph dev")

    except KeyboardInterrupt:
        print("\n🛑 调试会话结束")

if __name__ == "__main__":
    main()
