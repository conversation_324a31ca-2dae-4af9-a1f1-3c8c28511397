#!/usr/bin/env python3
"""
调试启动脚本 - 支持 VSCode 远程调试
"""

import debugpy
import os
import sys

# 启用调试服务器
debugpy.listen(("localhost", 5678))
print("等待调试器连接...")
print("在 VSCode 中按 F5 或运行调试配置来连接")

# 可选：等待调试器连接（如果你想在启动时就暂停）
# debugpy.wait_for_client()

# 导入并启动你的应用
if __name__ == "__main__":
    # 添加项目路径
    sys.path.insert(0, 'src')
    
    # 启动 langgraph dev 服务
    from langgraph.cli.dev import main as langgraph_dev_main
    
    # 模拟 langgraph dev 命令的参数
    sys.argv = ['langgraph', 'dev']
    
    try:
        langgraph_dev_main()
    except KeyboardInterrupt:
        print("调试会话结束")
