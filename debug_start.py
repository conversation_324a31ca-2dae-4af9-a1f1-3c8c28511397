#!/usr/bin/env python3
"""
调试启动脚本 - 支持 VSCode 远程调试
"""

import debugpy
import os
import sys
import subprocess
from pathlib import Path

def main():
    # 启用调试服务器
    debugpy.listen(("localhost", 5678))
    print("🔧 调试服务器已启动在端口 5678")
    print("📍 在 VSCode 中按 F5 或运行 'Python: 附加到 AlphaFi Agent' 调试配置来连接")
    print("⏳ 等待调试器连接...")

    # 等待调试器连接（可选）
    # debugpy.wait_for_client()
    # print("✅ 调试器已连接！")

    # 设置环境变量
    env = os.environ.copy()
    env['PYTHONPATH'] = str(Path(__file__).parent / 'src')

    print("🚀 启动 langgraph dev...")

    try:
        # 方法1: 使用 subprocess 启动 langgraph dev
        result = subprocess.run([
            sys.executable, '-m', 'langgraph', 'dev'
        ], env=env, cwd=Path(__file__).parent)

    except KeyboardInterrupt:
        print("\n🛑 调试会话结束")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

        # 方法2: 备用启动方式
        print("🔄 尝试备用启动方式...")
        try:
            # 添加项目路径
            sys.path.insert(0, str(Path(__file__).parent / 'src'))

            # 直接导入并运行应用
            from alphafi_agent.app import app
            import uvicorn

            print("🌐 启动 FastAPI 应用...")
            uvicorn.run(app, host="0.0.0.0", port=8000)

        except Exception as e2:
            print(f"❌ 备用启动也失败: {e2}")

if __name__ == "__main__":
    main()
