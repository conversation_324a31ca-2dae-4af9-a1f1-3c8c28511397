# AlphaFi Agent 图片处理功能指南

## 概述

AlphaFi Agent 现在支持处理MCP工具返回的图片数据，能够分析图片内容并基于图片信息回答用户问题。这对于DeFi领域的图表分析、协议架构理解等场景非常有用。

## 功能特性

- ✅ 自动检测MCP工具返回的图片数据
- ✅ 将base64编码的图片转换为模型可理解的格式
- ✅ 支持多模态模型进行图片分析
- ✅ 智能路由到图片分析节点
- ✅ 专门的图片分析提示词优化

## 工作流程

### 1. 用户查询
用户提出包含图片分析需求的问题：
```
"请帮我分析这个DeFi协议的收益率数据"
```

### 2. 意图识别
系统识别为DeFi知识查询意图，路由到相应处理节点。

### 3. MCP工具调用
调用配置的MCP工具（如morphik），工具返回包含图片的数据：
```json
{
  "content": "Retrieved 1 chunks:",
  "artifact": [
    {
      "type": "image",
      "data": "iVBORw0KGgoAAAANSUhEUgAABnYAAAkjCAIAAACzq+aSAAEAAElEQVR4nOz9dyDV...",
      "mimeType": "image/png",
      "annotations": null,
      "meta": null
    }
  ],
  "status": "success"
}
```

### 4. 图片数据处理
`process_tool_message_with_artifacts()` 函数处理图片数据：
- 提取base64编码的图片数据
- 转换为data URL格式
- 构建多模态消息内容

### 5. 智能路由
`tool_router()` 函数检测到图片内容，路由到图片分析节点。

### 6. 图片分析
`handle_image_analysis()` 函数使用专门的提示词分析图片：
- 分析图表、数据、文字等视觉信息
- 识别DeFi相关的协议、代币、收益率信息
- 结合专业知识生成回答

## 核心函数说明

### process_tool_message_with_artifacts()
```python
async def process_tool_message_with_artifacts(tool_msg):
    """
    处理包含图片等artifact的工具消息，将其转换为模型可以理解的格式
    
    Args:
        tool_msg: 包含artifact的工具消息
        
    Returns:
        处理后的消息，包含图片内容
    """
```

**功能：**
- 检查消息是否包含artifact字段
- 提取图片数据并转换为data URL格式
- 构建多模态消息内容
- 返回LangChain ToolMessage格式

### handle_image_analysis()
```python
async def handle_image_analysis(state: State):
    """
    专门处理包含图片的DeFi知识查询，分析图片内容并回答问题
    """
```

**功能：**
- 使用专门的图片分析提示词
- 将图片和上下文一起发送给模型
- 生成基于图片内容的专业回答

### tool_router() 增强
```python
def tool_router(state: State):
    """
    增强的路由函数，支持图片内容检测
    """
```

**新增功能：**
- 检测最近消息中的图片内容
- 支持artifact和image_url两种格式
- 智能路由到图片分析节点

## 配置要求

### 1. MCP服务器配置
在 `mcp_config.py` 中配置支持图片返回的MCP服务器：

```python
MCP_SERVER_CONFIG_RAG = {
  "morphik": {
    "transport": "stdio",
    "command": "npx",
    "args": [
      "-y",
      "/Users/<USER>/git/morphik-mcp",
      "--uri=http://************:8080"
    ]
  }
}
```

### 2. 模型配置
需要使用支持多模态输入的模型：

```bash
# 推荐使用Claude 3.5 Sonnet
MODEL_PROVIDER=anthropic
MODEL=claude-3-5-sonnet-20241022

# 或者使用OpenAI GPT-4V
# MODEL_PROVIDER=openai
# MODEL=gpt-4-vision-preview
```

### 3. 依赖包
确保安装了正确版本的依赖：

```bash
pip install langchain>=0.3.8
pip install langchain-core>=0.3.8
pip install langchain-mcp-adapters>=0.1.9
```

## 使用示例

### 基本使用
```python
from alphafi_agent.alphafi_graph import alphafi_graph

# 用户查询
user_input = "请分析这个DeFi协议的收益率图表"

# 执行查询
result = await alphafi_graph.ainvoke({
    "messages": [{"role": "user", "content": user_input}]
})
```

### 测试图片处理
```python
# 运行测试
python tests/test_image_processing.py

# 查看示例
python examples/image_processing_example.py
```

## 支持的图片格式

- PNG (image/png)
- JPEG (image/jpeg)
- GIF (image/gif)
- WebP (image/webp)

## 应用场景

### DeFi数据分析
- 收益率图表分析
- TVL趋势图解读
- 价格走势分析
- 流动性分布图

### 协议理解
- 协议架构图解析
- 流程图说明
- 代币经济模型图

### 风险评估
- 风险矩阵图分析
- 安全审计报告图表
- 历史事件时间线

## 故障排除

### 常见问题

1. **图片无法显示**
   - 检查base64数据是否完整
   - 确认mimeType格式正确
   - 验证模型是否支持多模态

2. **路由不正确**
   - 检查tool_router函数逻辑
   - 确认图片检测条件
   - 验证节点配置

3. **分析结果不准确**
   - 优化图片分析提示词
   - 提供更多上下文信息
   - 使用更强的多模态模型

### 调试方法

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 检查消息格式
print(f"消息类型: {type(message)}")
print(f"消息内容: {message.content}")
print(f"是否有artifact: {hasattr(message, 'artifact')}")
```

## 性能优化

1. **图片大小控制**
   - 建议图片大小不超过5MB
   - 使用适当的压缩比例
   - 考虑图片预处理

2. **缓存策略**
   - 缓存处理后的图片数据
   - 避免重复转换
   - 使用内存缓存

3. **并发处理**
   - 支持多个图片并行处理
   - 异步处理图片转换
   - 优化内存使用

## 扩展功能

### 未来计划
- 支持更多图片格式
- 添加图片预处理功能
- 集成OCR文字识别
- 支持图片生成功能

### 自定义扩展
可以通过继承和重写相关函数来扩展功能：

```python
class CustomImageProcessor:
    async def process_image(self, image_data):
        # 自定义图片处理逻辑
        pass
```

## 总结

AlphaFi Agent的图片处理功能为DeFi领域的多模态查询提供了强大支持。通过智能的图片检测、格式转换和专业分析，用户可以更直观地理解复杂的DeFi数据和协议信息。
