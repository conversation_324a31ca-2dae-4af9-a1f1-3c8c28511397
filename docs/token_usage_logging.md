# Token 使用统计功能说明

## 概述

我们已经为 AlphaFi Agent 添加了完整的 token 使用统计功能，可以记录每次 LLM 调用的输入 token、输出 token 和总 token 数量。

## 功能特点

1. **自动记录**: 所有 LLM 调用都会自动记录 token 使用情况
2. **多格式支持**: 支持不同 LLM 提供商的 token 统计格式（OpenAI、Anthropic、Google 等）
3. **详细日志**: 包含函数名称标识，便于追踪具体调用位置
4. **错误处理**: 即使获取 token 信息失败也不会影响正常功能

## 实现位置

### 1. 核心工具函数
- **文件**: `src/alphafi_agent/utils.py`
- **函数**: `log_token_usage(msg, function_name)`
- **功能**: 统一处理不同格式的 token 使用信息并记录到日志

### 2. 已添加统计的函数

#### alphafi_graph.py 中的函数：
- `recognize_intent()` - 意图识别
- `handle_on_chain_operation()` - 链上操作处理
- `handle_recommendation_action()` - 推荐操作处理
- `handle_token_market_data()` - Token 市场数据处理
- `handle_yield_pool_data()` - 收益池数据处理
- `handle_defi_knowledge_query()` - DeFi 知识查询处理
- `handle_output()` - 输出处理

#### graph.py 中的函数：
- `call_model()` - 主要模型调用

## 日志格式

每次 LLM 调用会产生类似以下的日志：

```
2024-01-XX XX:XX:XX - alphafi_agent.utils - INFO - 开始调用 LLM (handle_defi_knowledge_query)
2024-01-XX XX:XX:XX - alphafi_agent.utils - INFO - LLM Token 使用统计 (handle_defi_knowledge_query): 输入 tokens: 1250, 输出 tokens: 180, 总计 tokens: 1430
```

## 支持的 Token 格式

### OpenAI 格式
```json
{
  "response_metadata": {
    "token_usage": {
      "prompt_tokens": 1250,
      "completion_tokens": 180,
      "total_tokens": 1430
    }
  }
}
```

### Anthropic/Google 格式
```json
{
  "usage_metadata": {
    "input_tokens": 1250,
    "output_tokens": 180,
    "total_tokens": 1430
  }
}
```

或

```json
{
  "response_metadata": {
    "usage": {
      "input_tokens": 1250,
      "output_tokens": 180,
      "total_tokens": 1430
    }
  }
}
```

## 测试方法

### 1. 运行测试脚本
```bash
python test_token_usage.py
```

### 2. 查看实际应用日志
启动应用后，每次 LLM 调用都会在日志中显示 token 使用情况。

### 3. 监控特定函数
可以通过搜索日志中的函数名来查看特定操作的 token 消耗：
```bash
grep "handle_defi_knowledge_query" logs/app.log
```

## 配置说明

### 日志级别
确保日志级别设置为 INFO 或更低，以便显示 token 统计信息：

```python
# src/alphafi_agent/__init__.py
logging.basicConfig(
    level=logging.INFO,  # 确保是 INFO 级别
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.StreamHandler()
    ]
)
```

### 环境变量
不需要额外的环境变量配置，token 统计功能会自动工作。

## 故障排除

### 1. 看不到 token 统计信息
- 检查日志级别是否为 INFO
- 确认使用的 LLM 提供商是否支持 token 统计
- 查看是否有错误日志

### 2. Token 信息显示为 "N/A"
- 某些本地模型（如 Ollama）可能不提供 token 统计
- 检查模型响应格式是否符合预期

### 3. 统计信息不准确
- 不同提供商的 token 计算方式可能略有差异
- 确认使用的是最新版本的 LangChain

## 扩展功能

### 添加新的调用点
如果需要为新的 LLM 调用添加 token 统计，只需：

1. 在调用前添加日志：
```python
logger.info("开始调用 LLM (your_function_name)")
```

2. 在调用后添加统计：
```python
utils.log_token_usage(response, "your_function_name")
```

### 自定义统计格式
可以修改 `utils.log_token_usage()` 函数来支持新的 token 格式或添加更多统计信息。

## 性能影响

- Token 统计功能对性能影响极小
- 主要开销是日志写入，可以通过调整日志级别来控制
- 不会影响 LLM 调用的正常执行
