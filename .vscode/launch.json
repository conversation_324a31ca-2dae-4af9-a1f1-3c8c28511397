{"version": "0.2.0", "configurations": [{"name": "Python: 附加到 AlphaFi Agent", "type": "python", "request": "attach", "connect": {"host": "localhost", "port": 5678}, "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "."}], "justMyCode": false}, {"name": "Python: 直接启动 AlphaFi Agent", "type": "python", "request": "launch", "program": "${workspaceFolder}/debug_start.py", "console": "integratedTerminal", "justMyCode": false, "env": {"PYTHONPATH": "${workspaceFolder}/src"}}]}