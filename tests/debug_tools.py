#!/usr/bin/env python3
"""调试工具定义的脚本"""

import asyncio
import json

from langchain_mcp_adapters.client import MultiServerMCPClient
from alphafi_agent.mcp_config import MCP_SERVER_CONFIG_RAG
from alphafi_agent.protocol.lido import stake_action


async def debug_tools():
    """调试工具定义"""
    print("🔍 开始调试工具定义...")
    
    # 1. 检查 MCP 工具
    print("\n1. 检查 MCP 工具:")
    try:
        mcp_client = MultiServerMCPClient(MCP_SERVER_CONFIG_RAG)
        mcp_tools = await mcp_client.get_tools()
        print(f"   MCP 工具数量: {len(mcp_tools)}")
        for i, tool in enumerate(mcp_tools):
            print(f"   工具 {i}: {tool.name}")
            # 检查工具的参数定义
            if hasattr(tool, 'args_schema') and tool.args_schema:
                try:
                    if hasattr(tool.args_schema, 'schema'):
                        schema = tool.args_schema.schema()
                    elif hasattr(tool.args_schema, 'model_json_schema'):
                        schema = tool.args_schema.model_json_schema()
                    elif isinstance(tool.args_schema, dict):
                        schema = tool.args_schema
                    else:
                        schema = str(tool.args_schema)
                    print(f"      参数模式: {json.dumps(schema, indent=2, ensure_ascii=False)}")
                except Exception as e:
                    print(f"      ❌ 解析参数模式失败: {e}")
                    print(f"      参数模式类型: {type(tool.args_schema)}")
            else:
                print(f"      无参数模式")
    except Exception as e:
        print(f"   ❌ MCP 工具检查失败: {e}")
    
    # 2. 检查 stake_action 工具
    print("\n2. 检查 stake_action 工具:")
    try:
        print(f"   工具名称: {stake_action.name}")
        if hasattr(stake_action, 'args_schema') and stake_action.args_schema:
            schema = stake_action.args_schema.schema()
            print(f"   参数模式: {json.dumps(schema, indent=2, ensure_ascii=False)}")
        else:
            print(f"   无参数模式")
    except Exception as e:
        print(f"   ❌ stake_action 工具检查失败: {e}")
    

    # 3. 检查所有工具的组合
    print("\n4. 检查所有工具的组合:")
    try:
        all_tools = [*(await mcp_client.get_tools()), stake_action]
        print(f"   总工具数量: {len(all_tools)}")
        
        for i, tool in enumerate(all_tools):
            print(f"\n   工具 {i}: {tool.name}")
            if hasattr(tool, 'args_schema') and tool.args_schema:
                try:
                    if hasattr(tool.args_schema, 'schema'):
                        schema = tool.args_schema.schema()
                    elif hasattr(tool.args_schema, 'model_json_schema'):
                        schema = tool.args_schema.model_json_schema()
                    elif isinstance(tool.args_schema, dict):
                        schema = tool.args_schema
                    else:
                        schema = str(tool.args_schema)
                    print(f"      参数模式: {json.dumps(schema, indent=2, ensure_ascii=False)}")

                    # 检查是否有问题的参数定义
                    if isinstance(schema, dict) and 'properties' in schema:
                        for prop_name, prop_def in schema['properties'].items():
                            if 'type' in prop_def and prop_def['type'] == 'array':
                                if 'items' not in prop_def:
                                    print(f"      ⚠️  发现问题: 参数 '{prop_name}' 是数组类型但缺少 'items' 定义")
                                    print(f"         参数定义: {json.dumps(prop_def, indent=2, ensure_ascii=False)}")
                except Exception as e:
                    print(f"      ❌ 解析参数模式失败: {e}")
                    print(f"      参数模式类型: {type(tool.args_schema)}")
            else:
                print(f"      无参数模式")
                
    except Exception as e:
        print(f"   ❌ 工具组合检查失败: {e}")

if __name__ == "__main__":
    asyncio.run(debug_tools())
