"""
图片处理功能使用示例

这个示例展示了如何使用AlphaFi Agent处理MCP工具返回的图片数据
"""
import asyncio
import json
from alphafi_agent.alphafi_graph import alphafi_graph
from alphafi_agent.state import State
from langchain_core.messages import HumanMessage


async def simulate_image_query():
    """模拟包含图片的DeFi知识查询"""
    
    print("🚀 开始模拟图片处理流程...")
    
    # 1. 用户提问
    user_question = "请帮我分析一下这个DeFi协议的收益率数据"
    print(f"👤 用户问题: {user_question}")
    
    # 2. 创建初始状态
    initial_state = State(
        messages=[HumanMessage(content=user_question)],
        intent="defi_knowledge_query"
    )
    
    # 3. 模拟图的执行流程
    print("\n📊 执行图处理流程:")
    print("1. 识别意图 -> DeFi知识查询")
    print("2. 调用MCP工具 -> 返回包含图片的数据")
    print("3. 处理图片数据 -> 转换为模型可理解的格式")
    print("4. 图片分析 -> 模型分析图片内容")
    print("5. 生成回答 -> 基于图片内容回答用户问题")
    
    # 注意：这里只是演示流程，实际运行需要配置MCP服务器
    print("\n⚠️  注意：要实际运行此功能，需要:")
    print("1. 配置MCP服务器 (morphik)")
    print("2. 确保模型支持多模态输入 (如Claude 3.5 Sonnet)")
    print("3. 设置正确的环境变量")
    
    return True


def explain_image_processing_flow():
    """解释图片处理流程"""
    
    print("\n🔍 图片处理流程详解:")
    print("=" * 50)
    
    print("\n1. MCP工具返回图片数据:")
    sample_response = {
        "content": "Retrieved 1 chunks:",
        "artifact": [
            {
                "type": "image",
                "data": "iVBORw0KGgoAAAANSUhEUgAABnYAAAkjCAIAAACzq+aSAAEAAElEQVR4nOz9dyDV...",
                "mimeType": "image/png",
                "annotations": None,
                "meta": None
            }
        ],
        "status": "success"
    }
    print(json.dumps(sample_response, indent=2, ensure_ascii=False))
    
    print("\n2. process_tool_message_with_artifacts() 函数处理:")
    print("   - 提取base64图片数据")
    print("   - 转换为data URL格式: data:image/png;base64,...")
    print("   - 构建多模态消息内容")
    
    print("\n3. 转换后的消息格式:")
    converted_format = {
        "type": "tool",
        "content": [
            {"type": "text", "text": "Retrieved 1 chunks:"},
            {
                "type": "image_url",
                "image_url": {
                    "url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABnYAAAkjCAIAAACzq+aSAAEAAElEQVR4nOz9dyDV..."
                }
            }
        ],
        "tool_call_id": "call_id"
    }
    print(json.dumps(converted_format, indent=2, ensure_ascii=False))
    
    print("\n4. 图片分析节点 (handle_image_analysis):")
    print("   - 使用专门的图片分析提示词")
    print("   - 将图片和文本一起发送给模型")
    print("   - 模型分析图片内容并生成回答")
    
    print("\n5. 路由逻辑 (tool_router):")
    print("   - 检测消息中是否包含图片")
    print("   - 如果有图片，路由到 image_analysis 节点")
    print("   - 否则正常路由到输出节点")


def show_configuration_requirements():
    """显示配置要求"""
    
    print("\n⚙️  配置要求:")
    print("=" * 50)
    
    print("\n1. MCP服务器配置 (mcp_config.py):")
    mcp_config = '''
MCP_SERVER_CONFIG_RAG = {
  "morphik": {
    "transport": "stdio",
    "command": "npx",
    "args": [
      "-y",
      "/Users/<USER>/git/morphik-mcp",
      "--uri=http://10.1.177.121:8080"
    ]
  }
}
'''
    print(mcp_config)
    
    print("\n2. 环境变量设置:")
    env_vars = '''
# 模型配置 - 需要支持多模态的模型
MODEL_PROVIDER=anthropic
MODEL=claude-3-5-sonnet-20241022

# 或者使用OpenAI的GPT-4V
# MODEL_PROVIDER=openai  
# MODEL=gpt-4-vision-preview
'''
    print(env_vars)
    
    print("\n3. 依赖包要求:")
    dependencies = '''
# 确保安装了支持多模态的LangChain版本
langchain>=0.3.8
langchain-core>=0.3.8
langchain-mcp-adapters>=0.1.9
'''
    print(dependencies)


async def main():
    """主函数"""
    print("🖼️  AlphaFi Agent 图片处理功能示例")
    print("=" * 60)
    
    # 模拟查询流程
    await simulate_image_query()
    
    # 解释处理流程
    explain_image_processing_flow()
    
    # 显示配置要求
    show_configuration_requirements()
    
    print("\n✨ 总结:")
    print("现在AlphaFi Agent可以:")
    print("1. 接收MCP工具返回的图片数据")
    print("2. 将图片转换为模型可理解的格式")
    print("3. 使用多模态模型分析图片内容")
    print("4. 基于图片信息回答用户问题")
    
    print("\n🎯 使用场景:")
    print("- 分析DeFi协议的图表数据")
    print("- 解读收益率走势图")
    print("- 理解协议架构图")
    print("- 分析代币价格图表")


if __name__ == "__main__":
    asyncio.run(main())
