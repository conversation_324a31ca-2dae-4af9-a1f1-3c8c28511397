#!/usr/bin/env python3
"""
测试图片处理修复的简单脚本
"""
import asyncio
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from alphafi_agent.alphafi_graph import tool_handle_defi_knowledge_query, has_image_artifact
from alphafi_agent.state import State
from langchain_core.messages import ToolMessage, AIMessage, HumanMessage
from unittest.mock import AsyncMock, patch


def test_has_image_artifact():
    """测试图片artifact检测函数"""
    print("🧪 测试 has_image_artifact 函数...")
    
    # 测试字典格式
    dict_artifact = {"type": "image", "data": "test_data", "mimeType": "image/png"}
    assert has_image_artifact(dict_artifact) == True
    print("✅ 字典格式图片检测通过")

    # 测试对象格式
    class MockImageArtifact:
        def __init__(self):
            self.type = "image"
            self.data = "test_data"
            self.mimeType = "image/png"

    obj_artifact = MockImageArtifact()
    assert has_image_artifact(obj_artifact) == True
    print("✅ 对象格式图片检测通过")

    # 测试非图片类型
    non_image_artifact = {"type": "text", "data": "test_data"}
    assert has_image_artifact(non_image_artifact) == False
    print("✅ 非图片类型检测通过")


async def test_tool_handle_with_image_in_content():
    """测试处理content中已包含图片的工具消息"""
    print("\n🧪 测试 tool_handle_defi_knowledge_query 处理图片...")
    
    # 创建包含图片的工具消息（模拟实际情况）
    tool_msg = ToolMessage(
        content=[
            {"type": "text", "text": "Retrieved 1 chunks:"},
            {
                "type": "image_url", 
                "image_url": {
                    "url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABnYAAAkjCAIAAACzq+aSAAEAAElEQVR4nOz9dyDV..."
                }
            }
        ],
        tool_call_id="call_test",
        name="retrieve-chunks"
    )
    tool_msg.artifact = None  # 模拟实际情况，artifact为None
    
    # 创建AI消息（包含工具调用）
    ai_msg = AIMessage(
        content="",
        tool_calls=[{
            "id": "call_test",
            "name": "retrieve-chunks", 
            "args": {"query": "test"}
        }]
    )
    
    # 创建状态
    state = State(messages=[ai_msg])
    
    # Mock ToolNode和相关函数
    with patch('alphafi_agent.alphafi_graph.get_defi_knowledge_tools') as mock_get_tools, \
         patch('alphafi_agent.alphafi_graph.ToolNode') as mock_tool_node:
        
        mock_get_tools.return_value = []
        mock_tool_node_instance = AsyncMock()
        mock_tool_node_instance.ainvoke.return_value = [tool_msg]
        mock_tool_node.return_value = mock_tool_node_instance
        
        # 执行函数
        result = await tool_handle_defi_knowledge_query(state, {})
        
        # 验证结果
        messages = result["messages"]
        print(f"📊 返回消息数量: {len(messages)}")
        
        if len(messages) == 2:
            print("✅ 正确返回了2条消息（工具消息 + 分析请求）")
            
            # 检查第一个消息是否是原始工具消息
            if messages[0] == tool_msg:
                print("✅ 第一个消息是原始工具消息")
            else:
                print("❌ 第一个消息不是原始工具消息")
                
            # 检查第二个消息是否是分析请求
            if isinstance(messages[1], HumanMessage) and "请分析上面的图片内容" in messages[1].content:
                print("✅ 第二个消息是图片分析请求")
            else:
                print("❌ 第二个消息不是图片分析请求")
                print(f"实际内容: {messages[1].content}")
        else:
            print(f"❌ 期望2条消息，实际得到{len(messages)}条")
            for i, msg in enumerate(messages):
                print(f"消息{i}: {type(msg).__name__} - {getattr(msg, 'content', 'No content')}")


def test_content_image_detection():
    """测试检测content中图片的逻辑"""
    print("\n🧪 测试content中图片检测逻辑...")
    
    # 创建包含图片的消息
    msg_with_image = ToolMessage(
        content=[
            {"type": "text", "text": "Retrieved 1 chunks:"},
            {
                "type": "image_url", 
                "image_url": {
                    "url": "data:image/png;base64,test_data"
                }
            }
        ],
        tool_call_id="call_test",
        name="retrieve-chunks"
    )
    
    # 检测逻辑
    has_image = False
    if hasattr(msg_with_image, 'content') and isinstance(msg_with_image.content, list):
        for content_part in msg_with_image.content:
            if isinstance(content_part, dict) and content_part.get("type") == "image_url":
                has_image = True
                break
    
    if has_image:
        print("✅ 成功检测到content中的图片")
    else:
        print("❌ 未能检测到content中的图片")
    
    # 测试没有图片的消息
    msg_without_image = ToolMessage(
        content="Simple text content",
        tool_call_id="call_test2",
        name="retrieve-chunks"
    )
    
    has_image2 = False
    if hasattr(msg_without_image, 'content') and isinstance(msg_without_image.content, list):
        for content_part in msg_without_image.content:
            if isinstance(content_part, dict) and content_part.get("type") == "image_url":
                has_image2 = True
                break
    
    if not has_image2:
        print("✅ 正确识别没有图片的消息")
    else:
        print("❌ 错误地检测到了图片")


async def main():
    """主测试函数"""
    print("🚀 开始测试图片处理修复...")
    print("=" * 50)
    
    try:
        # 测试基础函数
        test_has_image_artifact()
        
        # 测试图片检测逻辑
        test_content_image_detection()
        
        # 测试主要修复功能
        await test_tool_handle_with_image_in_content()
        
        print("\n" + "=" * 50)
        print("🎉 所有测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
