#!/usr/bin/env python3
"""
简化的调试启动脚本 - 直接启动 FastAPI 应用
"""

import os
import sys
import asyncio
from pathlib import Path

# 设置环境变量禁用调试警告
os.environ['PYDEVD_DISABLE_FILE_VALIDATION'] = '1'

def main():
    # 添加项目路径
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root / 'src'))
    
    # 启用调试服务器
    try:
        import debugpy
        debugpy.listen(("localhost", 5678))
        print("🔧 调试服务器已启动在端口 5678")
        print("📍 在 VSCode 中按 F5 或运行 'Python: 附加到 AlphaFi Agent' 调试配置来连接")
        print("⏳ 等待调试器连接...")
        
        # 如果你想在启动时就等待调试器连接，取消下面的注释
        # debugpy.wait_for_client()
        # print("✅ 调试器已连接！")
        
    except ImportError:
        print("⚠️ debugpy 未安装，请运行: uv add debugpy --dev")
        return
    
    try:
        print("📦 导入应用模块...")
        from alphafi_agent.app import __global_init__, app
        import uvicorn
        
        print("🔧 初始化应用...")
        asyncio.run(__global_init__())
        
        print("🌐 启动 FastAPI 应用在 http://localhost:8000")
        print("🔍 现在你可以在 VSCode 中设置断点并连接调试器")
        print("📝 发送请求到应用来触发断点")
        
        # 启动应用
        uvicorn.run(
            app, 
            host="0.0.0.0", 
            port=8000, 
            log_level="info",
            reload=False  # 禁用自动重载以避免调试问题
        )
        
    except Exception as e:
        print(f"❌ 应用启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
